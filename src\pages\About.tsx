/**
 * @fileoverview Página Sobre do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React from 'react';
import {
  FiBook,
  FiCalendar,
  FiCode,
  FiCoffee,
  FiGithub,
  FiGlobe,
  FiHeart,
  FiLinkedin,
  FiMail,
  FiMapPin,
  FiMessageCircle,
  FiStar,
  FiTarget,
  FiTrendingUp,
  FiTwitter,
  FiUser,
  FiUsers,
} from 'react-icons/fi';
import {
  SiDocker,
  SiFigma,
  SiGit,
  SiNodedotjs,
  SiPostgresql,
  SiReact,
  SiTailwindcss,
  SiTypescript,
  SiVercel,
} from 'react-icons/si';
import { MainLayout } from '../components/layout/MainLayout';

const About: React.FC = () => {
  const skills = [
    { name: 'React', icon: SiReact, color: 'text-blue-400' },
    { name: 'TypeScript', icon: SiTypescript, color: 'text-blue-500' },
    { name: 'Node.js', icon: SiNodedotjs, color: 'text-green-400' },
    { name: 'Tailwind', icon: SiTailwindcss, color: 'text-cyan-400' },
    { name: 'PostgreSQL', icon: SiPostgresql, color: 'text-blue-600' },
    { name: 'Vercel', icon: SiVercel, color: 'text-white' },
    { name: 'Docker', icon: SiDocker, color: 'text-blue-500' },
    { name: 'Git', icon: SiGit, color: 'text-orange-500' },
    { name: 'Figma', icon: SiFigma, color: 'text-purple-500' },
  ];

  const stats = [
    { label: 'Artigos Publicados', value: '150+', icon: FiBook },
    { label: 'Devs Alcançados', value: '50K+', icon: FiUsers },
    { label: 'Libraries Documentadas', value: '200+', icon: FiCode },
    { label: 'Projetos Open Source', value: '25+', icon: FiGithub },
    { label: 'Anos Ativo', value: '2', icon: FiCalendar },
    { label: 'Países Alcançados', value: '35', icon: FiGlobe },
  ];

  const socialLinks = [
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/in/blueprint-founder',
      icon: FiLinkedin,
      color: 'text-blue-500 hover:text-blue-400',
    },
    {
      name: 'GitHub',
      url: 'https://github.com/blueprint-tech',
      icon: FiGithub,
      color: 'text-white hover:text-gray-300',
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/blueprint_dev',
      icon: FiTwitter,
      color: 'text-sky-500 hover:text-sky-400',
    },
  ];

  return (
    <>
      <Helmet>
        <title>Sobre | Blueprint Blog</title>
        <meta
          name="description"
          content="Conheça a história do Blueprint Blog e seu fundador. Democratizando conhecimento técnico para a comunidade de desenvolvedores."
        />
        <meta name="robots" content="index, follow" />
      </Helmet>

      <MainLayout>
        <div className="min-h-screen bg-cyber-bg py-12">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Hero Section */}
            <div className="text-center mb-16">
              <div className="flex justify-center mb-6">
                <div className="relative">
                  <div className="p-6 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                    <FiUser className="w-16 h-16 text-neon-cyan" />
                  </div>
                  <div className="absolute -top-2 -right-2 p-2 bg-cyber-bg rounded-full border border-neon-cyan">
                    <FiHeart className="w-6 h-6 text-red-400" />
                  </div>
                </div>
              </div>
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
                Sobre o <span className="text-neon-cyan">Blueprint</span>
              </h1>
              <p className="text-cyber-muted text-xl max-w-3xl mx-auto leading-relaxed">
                Democratizando conhecimento técnico e agregando valor real à
                comunidade de desenvolvedores
              </p>
              <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-8"></div>
            </div>

            {/* Mission Section */}
            <section className="mb-16">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-cyber-surface">
                <div className="flex items-center gap-4 mb-8">
                  <FiTarget className="w-8 h-8 text-neon-cyan" />
                  <h2 className="text-3xl font-bold text-white">
                    Nossa Missão
                  </h2>
                </div>

                <div className="grid lg:grid-cols-2 gap-8 items-center">
                  <div>
                    <p className="text-cyber-text leading-relaxed text-lg mb-6">
                      O{' '}
                      <strong className="text-neon-cyan">Blueprint Blog</strong>{' '}
                      nasceu com uma missão clara: democratizar o conhecimento
                      técnico e agregar valor real à comunidade de
                      desenvolvedores. Acreditamos que a tecnologia deve ser
                      acessível a todos, independentemente do nível de
                      experiência.
                    </p>
                    <div className="bg-neon-cyan/10 p-6 rounded-lg border border-neon-cyan/30">
                      <h3 className="text-white font-semibold mb-3">
                        Nossos Valores:
                      </h3>
                      <ul className="text-cyber-text space-y-2">
                        <li>
                          ✓ <strong>Conhecimento acessível</strong> para todos
                          os níveis
                        </li>
                        <li>
                          ✓ <strong>Qualidade técnica</strong> sem sacrificar
                          clareza
                        </li>
                        <li>
                          ✓ <strong>Comunidade colaborativa</strong> e inclusiva
                        </li>
                        <li>
                          ✓ <strong>Inovação constante</strong> e experimentação
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-cyber-bg/50 p-6 rounded-lg border border-green-500/30 text-center">
                      <FiBook className="w-8 h-8 text-green-400 mx-auto mb-3" />
                      <h4 className="text-white font-semibold mb-2">
                        Tutoriais
                      </h4>
                      <p className="text-cyber-muted text-sm">
                        Step-by-step detalhados com código funcional
                      </p>
                    </div>
                    <div className="bg-cyber-bg/50 p-6 rounded-lg border border-blue-500/30 text-center">
                      <FiTrendingUp className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                      <h4 className="text-white font-semibold mb-2">
                        Notícias
                      </h4>
                      <p className="text-cyber-muted text-sm">
                        Últimas tendências do mundo tech
                      </p>
                    </div>
                    <div className="bg-cyber-bg/50 p-6 rounded-lg border border-purple-500/30 text-center">
                      <FiCode className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                      <h4 className="text-white font-semibold mb-2">
                        Libraries
                      </h4>
                      <p className="text-cyber-muted text-sm">
                        Documentação de famosas e gems escondidas
                      </p>
                    </div>
                    <div className="bg-cyber-bg/50 p-6 rounded-lg border border-orange-500/30 text-center">
                      <FiStar className="w-8 h-8 text-orange-400 mx-auto mb-3" />
                      <h4 className="text-white font-semibold mb-2">
                        Best Practices
                      </h4>
                      <p className="text-cyber-muted text-sm">
                        Dicas e insights de performance
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Author Section */}
            <section className="mb-16">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-cyber-surface">
                <div className="flex items-center gap-4 mb-8">
                  <FiUser className="w-8 h-8 text-neon-cyan" />
                  <h2 className="text-3xl font-bold text-white">
                    Sobre o Autor
                  </h2>
                </div>

                <div className="grid lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-2 space-y-6">
                    <div>
                      <h3 className="text-2xl font-bold text-neon-cyan mb-4">
                        Olá, eu sou o Fundador da Blueprint! 👋
                      </h3>
                      <p className="text-cyber-text leading-relaxed">
                        Sou um <strong>desenvolvedor apaixonado</strong> por
                        criar experiências digitais excepcionais e compartilhar
                        conhecimento com a comunidade. Com mais de{' '}
                        <strong className="text-neon-cyan">
                          5 anos de experiência
                        </strong>{' '}
                        no desenvolvimento de aplicações web, tenho me
                        especializado em tecnologias modernas e arquiteturas
                        escaláveis.
                      </p>
                    </div>

                    <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                      <h4 className="text-white font-semibold mb-4">
                        🚀 Blueprint - Minha Empresa
                      </h4>
                      <p className="text-cyber-text mb-4">
                        Fundei a{' '}
                        <strong className="text-neon-cyan">Blueprint</strong>{' '}
                        com o objetivo de entregar soluções tecnológicas
                        inovadoras para empresas que querem se destacar no mundo
                        digital.
                      </p>
                      <ul className="text-cyber-text space-y-2 text-sm">
                        <li>
                          • <strong>50+ projetos</strong> entregues com sucesso
                        </li>
                        <li>
                          • <strong>Arquiteturas</strong> para aplicações com
                          milhões de usuários
                        </li>
                        <li>
                          • <strong>Mentoria</strong> de desenvolvedores júnior
                          e pleno
                        </li>
                        <li>
                          • <strong>Speaker</strong> em eventos e meetups de
                          tecnologia
                        </li>
                      </ul>
                    </div>

                    <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                      <h4 className="text-white font-semibold mb-4">
                        🎓 Filosofia de Desenvolvimento
                      </h4>
                      <div className="grid md:grid-cols-2 gap-4">
                        <ul className="text-cyber-text space-y-2 text-sm">
                          <li>
                            • <strong>Developer Experience</strong> como
                            prioridade
                          </li>
                          <li>
                            • <strong>Performance First</strong> - otimização
                            desde o início
                          </li>
                        </ul>
                        <ul className="text-cyber-text space-y-2 text-sm">
                          <li>
                            • <strong>Accessibility</strong> - tecnologia
                            inclusiva
                          </li>
                          <li>
                            • <strong>Open Source</strong> - contribuição ativa
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Skills */}
                  <div className="space-y-6">
                    <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                      <h4 className="text-white font-semibold mb-4">
                        🔧 Stack Principal
                      </h4>
                      <div className="grid grid-cols-3 gap-3">
                        {skills.map((skill, index) => (
                          <div key={index} className="text-center group">
                            <div className="p-3 bg-cyber-surface rounded-lg border border-cyber-surface group-hover:border-neon-cyan/50 transition-colors">
                              <skill.icon
                                className={`w-6 h-6 mx-auto ${skill.color}`}
                              />
                            </div>
                            <span className="text-xs text-cyber-muted mt-1 block">
                              {skill.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-neon-cyan/10 p-6 rounded-lg border border-neon-cyan/30">
                      <h4 className="text-white font-semibold mb-4">
                        🏆 Reconhecimentos
                      </h4>
                      <ul className="text-cyber-text space-y-2 text-sm">
                        <li>
                          • <strong>Top 10</strong> blogs de dev em português
                        </li>
                        <li>
                          • <strong>Featured</strong> no GitHub Trending
                        </li>
                        <li>
                          • <strong>Speaker</strong> no React Conf Brazil
                        </li>
                        <li>
                          • <strong>Mentor</strong> no Google Developer Groups
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Stats Section */}
            <section className="mb-16">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-white mb-4">
                  📊 Estatísticas do Blog
                </h2>
                <p className="text-cyber-muted">
                  Números que refletem nosso impacto na comunidade
                </p>
              </div>

              <div className="grid md:grid-cols-3 lg:grid-cols-6 gap-4">
                {stats.map((stat, index) => (
                  <div
                    key={index}
                    className="bg-cyber-surface/50 backdrop-blur-sm rounded-lg p-6 border border-cyber-surface text-center group hover:border-neon-cyan/50 transition-colors">
                    <stat.icon className="w-8 h-8 text-neon-cyan mx-auto mb-3 group-hover:scale-110 transition-transform" />
                    <div className="text-2xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-cyber-muted text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </section>

            {/* Contact Section */}
            <section className="mb-16">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-cyber-surface">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-4">
                    📞 Vamos Conversar?
                  </h2>
                  <p className="text-cyber-muted">
                    Conecte-se comigo nas redes sociais ou entre em contato
                    diretamente
                  </p>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  {/* Social Links */}
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-6">
                      🤝 Redes Sociais
                    </h3>
                    <div className="space-y-4">
                      {socialLinks.map((social, index) => (
                        <a
                          key={index}
                          href={social.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`flex items-center gap-4 p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface hover:border-neon-cyan/50 transition-colors group ${social.color}`}>
                          <social.icon className="w-6 h-6 group-hover:scale-110 transition-transform" />
                          <span className="font-medium">{social.name}</span>
                          <span className="text-cyber-muted text-sm ml-auto">
                            @blueprint_dev
                          </span>
                        </a>
                      ))}
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-6">
                      📧 Contato Direto
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="flex items-center gap-3 mb-2">
                          <FiMail className="w-5 h-5 text-neon-cyan" />
                          <span className="text-white font-medium">
                            Email Pessoal
                          </span>
                        </div>
                        <a
                          href="mailto:<EMAIL>"
                          className="text-neon-cyan hover:underline">
                          <EMAIL>
                        </a>
                      </div>

                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="flex items-center gap-3 mb-2">
                          <FiUser className="w-5 h-5 text-blue-400" />
                          <span className="text-white font-medium">
                            Propostas Comerciais
                          </span>
                        </div>
                        <a
                          href="mailto:<EMAIL>"
                          className="text-blue-400 hover:underline">
                          <EMAIL>
                        </a>
                      </div>

                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="flex items-center gap-3 mb-2">
                          <FiUsers className="w-5 h-5 text-purple-400" />
                          <span className="text-white font-medium">
                            Colaborações
                          </span>
                        </div>
                        <a
                          href="mailto:<EMAIL>"
                          className="text-purple-400 hover:underline">
                          <EMAIL>
                        </a>
                      </div>

                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="flex items-center gap-3 mb-2">
                          <FiMessageCircle className="w-5 h-5 text-green-400" />
                          <span className="text-white font-medium">
                            WhatsApp
                          </span>
                        </div>
                        <a
                          href="https://wa.me/5521999999999"
                          className="text-green-400 hover:underline">
                          +55 21 99999-9999
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Collaboration Section */}
            <section className="mb-16">
              <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-cyber-surface">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-4">
                    🎯 Oportunidades de Colaboração
                  </h2>
                  <p className="text-cyber-muted">
                    Sempre aberto a novas parcerias e projetos interessantes
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-cyber-bg/30 p-6 rounded-lg border border-green-500/30 text-center">
                    <FiBook className="w-8 h-8 text-green-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-3">
                      Guest Posts
                    </h3>
                    <p className="text-cyber-muted text-sm">
                      Tutoriais exclusivos, case studies e reviews de
                      tecnologias emergentes
                    </p>
                  </div>

                  <div className="bg-cyber-bg/30 p-6 rounded-lg border border-blue-500/30 text-center">
                    <FiMessageCircle className="w-8 h-8 text-blue-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-3">Palestras</h3>
                    <p className="text-cyber-muted text-sm">
                      Talks técnicas, workshops e mentorias em eventos
                    </p>
                  </div>

                  <div className="bg-cyber-bg/30 p-6 rounded-lg border border-purple-500/30 text-center">
                    <FiCode className="w-8 h-8 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-3">Projetos</h3>
                    <p className="text-cyber-muted text-sm">
                      Desenvolvimento de soluções desafiadoras e consultoria
                      técnica
                    </p>
                  </div>

                  <div className="bg-cyber-bg/30 p-6 rounded-lg border border-orange-500/30 text-center">
                    <FiUsers className="w-8 h-8 text-orange-400 mx-auto mb-4" />
                    <h3 className="text-white font-semibold mb-3">
                      Treinamentos
                    </h3>
                    <p className="text-cyber-muted text-sm">
                      Workshops corporativos e mentoria para equipes de
                      desenvolvimento
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Support Section */}
            <section className="mb-16">
              <div className="bg-gradient-to-r from-neon-cyan/10 to-purple-500/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-neon-cyan/30">
                <div className="text-center mb-8">
                  <FiHeart className="w-12 h-12 text-red-400 mx-auto mb-4" />
                  <h2 className="text-3xl font-bold text-white mb-4">
                    💝 Apoie o Projeto
                  </h2>
                  <p className="text-cyber-muted">
                    Se nosso conteúdo te ajudou, considere apoiar o projeto para
                    mantermos tudo gratuito e de qualidade
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                      <FiCoffee className="w-5 h-5 text-amber-400" />
                      Buy Me a Coffee
                    </h3>
                    <div className="space-y-4">
                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="text-white font-medium mb-1">PIX</div>
                        <div className="text-neon-cyan">
                          <EMAIL>
                        </div>
                      </div>
                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="text-white font-medium mb-1">
                          PayPal
                        </div>
                        <a
                          href="https://paypal.me/blueprintblog"
                          className="text-blue-400 hover:underline">
                          paypal.me/blueprintblog
                        </a>
                      </div>
                      <div className="bg-cyber-bg/50 p-4 rounded-lg border border-cyber-surface">
                        <div className="text-white font-medium mb-1">Ko-fi</div>
                        <a
                          href="https://ko-fi.com/blueprintdev"
                          className="text-purple-400 hover:underline">
                          ko-fi.com/blueprintdev
                        </a>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                      <FiStar className="w-5 h-5 text-yellow-400" />
                      Outras Formas de Apoiar
                    </h3>
                    <ul className="space-y-3 text-cyber-text">
                      <li className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-neon-cyan rounded-full"></div>
                        <strong>Compartilhe</strong> nossos artigos nas redes
                        sociais
                      </li>
                      <li className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <strong>Contribua</strong> com correções e sugestões no
                        GitHub
                      </li>
                      <li className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <strong>Indique</strong> o blog para outros
                        desenvolvedores
                      </li>
                      <li className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                        <strong>Deixe feedback</strong> nos comentários dos
                        posts
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </section>

            {/* Footer CTA */}
            <div className="text-center">
              <div className="bg-cyber-surface/30 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                <h3 className="text-2xl font-bold text-white mb-4">
                  Pronto para <span className="text-neon-cyan">evoluir</span>{' '}
                  como desenvolvedor?
                </h3>
                <p className="text-cyber-muted mb-6">
                  Junte-se à nossa comunidade e receba os melhores conteúdos
                  diretamente no seu email
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/blog"
                    className="inline-block bg-neon-cyan text-cyber-bg font-semibold px-8 py-3 rounded-lg hover:bg-neon-cyan/90 transition-colors">
                    Explorar Artigos
                  </a>
                  <a
                    href="#newsletter"
                    className="inline-block border border-neon-cyan text-neon-cyan font-semibold px-8 py-3 rounded-lg hover:bg-neon-cyan/10 transition-colors">
                    Assinar Newsletter
                  </a>
                </div>

                <div className="mt-8 pt-6 border-t border-cyber-surface">
                  <p className="text-cyber-muted text-sm">
                    <FiMapPin className="inline w-4 h-4 mr-1" />
                    Casimiro de Abreu, Rio de Janeiro, Brasil
                  </p>
                  <p className="text-cyber-muted text-xs mt-2">
                    Feito com 💙 e muito ☕ por um desenvolvedor apaixonado por
                    tecnologia e comunidade.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    </>
  );
};

export default About;
