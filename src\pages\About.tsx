/**
 * @fileoverview Página Sobre do Blueprint Blog v2
 * <AUTHOR> Blog Team
 * @version 2.0.0
 */

import React from 'react';
import {
  FiBook,
  FiCode,
  FiCoffee,
  FiGithub,
  FiHeart,
  FiLinkedin,
  FiMail,
  FiMapPin,
  FiTarget,
  FiTrendingUp,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ser,
} from 'react-icons/fi';
import {
  SiDocker,
  SiFigma,
  SiGit,
  SiNodedotjs,
  SiPostgresql,
  SiReact,
  SiTailwindcss,
  SiTypescript,
  SiVercel,
} from 'react-icons/si';
import { JsonLdSchema, SEOHead } from '../components/seo';
import useSEO from '../hooks/useSEO';

// ============================================================================
// INTERFACES
// ============================================================================

interface AboutProps {
  className?: string;
}

// ============================================================================
// COMPONENTE
// ============================================================================

/**
 * Página Sobre do Blueprint Blog
 */
export const About: React.FC<AboutProps> = () => {
  // SEO para página sobre
  const seoData = useSEO({
    pageType: 'page',
    customTitle: 'Sobre | Blueprint Blog',
    customDescription:
      'Conheça a história do Blueprint Blog e seu fundador. Democratizando conhecimento técnico para a comunidade de desenvolvedores.',
    customKeywords: [
      'sobre',
      'blueprint blog',
      'história',
      'fundador',
      'missão',
      'valores',
      'tecnologia',
    ],
  });

  const skills = [
    { name: 'React', icon: SiReact, color: 'text-blue-400' },
    { name: 'TypeScript', icon: SiTypescript, color: 'text-blue-500' },
    { name: 'Node.js', icon: SiNodedotjs, color: 'text-green-400' },
    { name: 'Tailwind', icon: SiTailwindcss, color: 'text-cyan-400' },
    { name: 'PostgreSQL', icon: SiPostgresql, color: 'text-blue-600' },
    { name: 'Vercel', icon: SiVercel, color: 'text-white' },
    { name: 'Docker', icon: SiDocker, color: 'text-blue-500' },
    { name: 'Git', icon: SiGit, color: 'text-orange-500' },
    { name: 'Figma', icon: SiFigma, color: 'text-purple-500' },
  ];

  // Stats para uso futuro quando tivermos números reais
  /*
  const stats = [
    { label: 'Artigos Publicados', value: '150+', icon: FiBook },
    { label: 'Devs Alcançados', value: '50K+', icon: FiUsers },
    { label: 'Libraries Documentadas', value: '200+', icon: FiCode },
    { label: 'Projetos Open Source', value: '25+', icon: FiGithub },
    { label: 'Anos Ativo', value: '2', icon: FiCalendar },
    { label: 'Países Alcançados', value: '35', icon: FiGlobe },
  ];
  */

  const socialLinks = [
    {
      name: 'LinkedIn',
      url: 'https://linkedin.com/in/blueprint-founder',
      icon: FiLinkedin,
      color: 'text-blue-500 hover:text-blue-400',
    },
    {
      name: 'GitHub',
      url: 'https://github.com/blueprint-tech',
      icon: FiGithub,
      color: 'text-white hover:text-gray-300',
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/blueprint_dev',
      icon: FiTwitter,
      color: 'text-sky-500 hover:text-sky-400',
    },
  ];

  return (
    <>
      {/* SEO Head */}
      <SEOHead seo={seoData} />

      {/* JSON-LD Schema */}
      <JsonLdSchema
        type="page"
        breadcrumbs={[
          { name: 'Home', url: '/' },
          { name: 'Sobre', url: '/about' },
        ]}
      />

      <div className="min-h-screen bg-cyber-bg py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="p-6 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                  <FiUser className="w-16 h-16 text-neon-cyan" />
                </div>
                <div className="absolute -top-2 -right-2 p-2 bg-cyber-bg rounded-full border border-neon-cyan">
                  <FiHeart className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              Sobre o <span className="text-neon-cyan">Blueprint</span>
            </h1>
            <p className="text-cyber-muted text-xl max-w-3xl mx-auto leading-relaxed">
              Democratizando conhecimento técnico e agregando valor real à
              comunidade de desenvolvedores
            </p>
          </div>

          {/* Mission Section */}
          <section className="mb-16">
            <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-cyber-border">
              <div className="grid lg:grid-cols-2 gap-8 items-center">
                <div>
                  <p className="text-cyber-text leading-relaxed text-lg mb-6">
                    O <strong className="text-neon-cyan">Blueprint Blog</strong>{' '}
                    nasceu com uma missão clara: democratizar o conhecimento
                    técnico e agregar valor real à comunidade de
                    desenvolvedores. Acreditamos que a tecnologia deve ser
                    acessível a todos, independentemente do nível de
                    experiência.
                  </p>
                  <div className="bg-neon-cyan/10 p-6 rounded-lg border border-neon-cyan/30">
                    <h3 className="text-white font-semibold mb-3">
                      Nossos Valores:
                    </h3>
                    <ul className="text-cyber-text space-y-2">
                      <li>
                        ✓ <strong>Conhecimento acessível</strong> para todos os
                        níveis
                      </li>
                      <li>
                        ✓ <strong>Qualidade técnica</strong> sem sacrificar
                        clareza
                      </li>
                      <li>
                        ✓ <strong>Comunidade colaborativa</strong> e inclusiva
                      </li>
                      <li>
                        ✓ <strong>Inovação constante</strong> e aprendizado
                        contínuo
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-green-500/30 text-center">
                    <FiBook className="w-8 h-8 text-green-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Tutoriais</h4>
                    <p className="text-cyber-muted text-sm">
                      Step-by-step detalhados com código funcional
                    </p>
                  </div>
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-blue-500/30 text-center">
                    <FiTrendingUp className="w-8 h-8 text-blue-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Notícias</h4>
                    <p className="text-cyber-muted text-sm">
                      Últimas tendências do mundo tech
                    </p>
                  </div>
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-purple-500/30 text-center">
                    <FiCode className="w-8 h-8 text-purple-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Reviews</h4>
                    <p className="text-cyber-muted text-sm">
                      Análises profundas de ferramentas e frameworks
                    </p>
                  </div>
                  <div className="bg-cyber-bg/50 p-6 rounded-lg border border-yellow-500/30 text-center">
                    <FiTarget className="w-8 h-8 text-yellow-400 mx-auto mb-3" />
                    <h4 className="text-white font-semibold mb-2">Cases</h4>
                    <p className="text-cyber-muted text-sm">
                      Estudos de caso reais e práticos
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Stats Section - Desabilitado até termos números reais */}
          {/*
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">
              📊 Nossos <span className="text-neon-cyan">Números</span>
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="bg-cyber-surface/30 p-6 rounded-lg border border-cyber-border text-center">
                  <stat.icon className="w-8 h-8 text-neon-cyan mx-auto mb-3" />
                  <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-cyber-muted text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </section>
          */}

          {/* Tech Stack */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">
              🛠️ Nossa <span className="text-neon-cyan">Stack</span>
            </h2>
            <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-6">
              {skills.map((skill, index) => (
                <div
                  key={index}
                  className="bg-cyber-surface/30 p-4 rounded-lg border border-cyber-border text-center group hover:border-neon-cyan/50 transition-colors">
                  <skill.icon
                    className={`w-8 h-8 mx-auto mb-2 ${skill.color} group-hover:scale-110 transition-transform`}
                  />
                  <div className="text-cyber-text text-sm font-medium">
                    {skill.name}
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Contact Section */}
          <section className="mb-16">
            <div className="bg-gradient-to-r from-neon-cyan/10 to-purple-500/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-neon-cyan/30">
              <div className="text-center mb-8">
                <FiMail className="w-12 h-12 text-neon-cyan mx-auto mb-4" />
                <h2 className="text-3xl font-bold text-white mb-4">
                  💬 Vamos Conversar
                </h2>
                <p className="text-cyber-muted">
                  Tem alguma dúvida, sugestão ou quer colaborar? Entre em
                  contato!
                </p>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {/* Social Links */}
                <div>
                  <h3 className="text-xl font-semibold text-white mb-6">
                    🤝 Redes Sociais
                  </h3>
                  <div className="space-y-4">
                    {socialLinks.map((social, index) => (
                      <a
                        key={index}
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`flex items-center gap-4 p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface hover:border-neon-cyan/50 transition-colors group ${social.color}`}>
                        <social.icon className="w-6 h-6 group-hover:scale-110 transition-transform" />
                        <span className="font-medium">{social.name}</span>
                        <span className="text-cyber-muted text-sm ml-auto">
                          @blueprint_dev
                        </span>
                      </a>
                    ))}
                  </div>
                </div>

                {/* Contact Info */}
                <div>
                  <h3 className="text-xl font-semibold text-white mb-6">
                    📧 Contato Direto
                  </h3>
                  <div className="space-y-4">
                    <div className="p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface">
                      <div className="flex items-center gap-3 mb-2">
                        <FiMail className="w-5 h-5 text-neon-cyan" />
                        <span className="text-white font-medium">Email</span>
                      </div>
                      <p className="text-cyber-muted">
                        <EMAIL>
                      </p>
                    </div>

                    <div className="p-4 bg-cyber-bg/50 rounded-lg border border-cyber-surface">
                      <div className="flex items-center gap-3 mb-2">
                        <FiMapPin className="w-5 h-5 text-neon-cyan" />
                        <span className="text-white font-medium">
                          Localização
                        </span>
                      </div>
                      <p className="text-cyber-muted">
                        Casimiro de Abreu, Rio de Janeiro, Brasil
                      </p>
                    </div>
                  </div>

                  <div className="mt-6">
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-block border border-neon-cyan text-neon-cyan font-semibold px-8 py-3 rounded-lg hover:bg-neon-cyan/10 transition-colors">
                      Enviar Email
                    </a>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-cyber-surface">
                <p className="text-cyber-muted text-sm text-center">
                  <FiCoffee className="inline w-4 h-4 mr-1" />
                  Feito com 💙 e muito ☕ por um desenvolvedor apaixonado por
                  tecnologia e comunidade.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </>
  );
};

export default About;
