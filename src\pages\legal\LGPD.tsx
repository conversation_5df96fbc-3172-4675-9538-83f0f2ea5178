import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import {
  FiShield,
  FiUser,
  <PERSON>Lock,
  FiMail,
  FiFileText,
  FiEye,
} from 'react-icons/fi';
import MainLayout from '../components/layout/MainLayout';

const LGPD: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'rights' | 'contact'>(
    'overview'
  );

  return (
    <>
      <Helmet>
        <title>LGPD - Lei Geral de Proteção de Dados | Blueprint Blog</title>
        <meta
          name="description"
          content="Conformidade com a LGPD - Seus direitos e como protegemos seus dados pessoais no Blueprint Blog"
        />
        <meta name="robots" content="index, follow" />
      </Helmet>

      <MainLayout>
        <div className="min-h-screen bg-cyber-bg py-12">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Header */}
            <div className="text-center mb-12">
              <div className="flex justify-center mb-6">
                <div className="p-4 bg-neon-cyan/20 rounded-full border border-neon-cyan">
                  <FiShield className="w-12 h-12 text-neon-cyan" />
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                <span className="text-neon-cyan">LGPD</span> Compliance
              </h1>
              <p className="text-cyber-muted text-lg max-w-3xl mx-auto">
                Lei Geral de Proteção de Dados - Transparência total sobre como
                tratamos seus dados pessoais
              </p>
              <div className="h-1 w-32 bg-neon-cyan mx-auto rounded-full mt-6"></div>
            </div>

            {/* Navigation Tabs */}
            <div className="flex justify-center mb-12">
              <div className="bg-cyber-surface/50 p-2 rounded-full border border-cyber-surface">
                <div className="flex space-x-2">
                  {[
                    { id: 'overview', label: 'Visão Geral', icon: FiEye },
                    { id: 'rights', label: 'Seus Direitos', icon: FiUser },
                    { id: 'contact', label: 'Contato', icon: FiMail },
                  ].map(({ id, label, icon: Icon }) => (
                    <button
                      key={id}
                      onClick={() => setActiveTab(id as any)}
                      className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all ${
                        activeTab === id
                          ? 'bg-neon-cyan text-cyber-bg font-semibold'
                          : 'text-cyber-muted hover:text-white hover:bg-cyber-surface'
                      }`}>
                      <Icon className="w-4 h-4" />
                      <span>{label}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2">
                {activeTab === 'overview' && (
                  <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                    <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                      Lei Geral de Proteção de Dados
                    </h2>

                    <div className="space-y-8">
                      <section>
                        <h3 className="text-xl font-semibold text-white mb-4">
                          O que é a LGPD?
                        </h3>
                        <p className="text-cyber-text leading-relaxed mb-4">
                          A Lei Geral de Proteção de Dados (Lei nº 13.709/2018)
                          é uma legislação brasileira que regula o tratamento de
                          dados pessoais e garante maior controle e
                          transparência sobre como suas informações são
                          coletadas, usadas e compartilhadas.
                        </p>
                        <div className="bg-cyber-bg/50 p-6 rounded-lg border border-neon-cyan/20">
                          <h4 className="text-white font-semibold mb-3">
                            Nosso Compromisso:
                          </h4>
                          <ul className="text-cyber-text space-y-2">
                            <li>
                              ✓ Transparência total sobre coleta e uso de dados
                            </li>
                            <li>
                              ✓ Respeito aos seus direitos como titular dos
                              dados
                            </li>
                            <li>
                              ✓ Segurança máxima no armazenamento e
                              processamento
                            </li>
                            <li>
                              ✓ Conformidade integral com todos os artigos da
                              LGPD
                            </li>
                          </ul>
                        </div>
                      </section>

                      <section>
                        <h3 className="text-xl font-semibold text-white mb-4">
                          Dados que Coletamos
                        </h3>
                        <div className="grid md:grid-cols-2 gap-4">
                          <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                            <h4 className="text-neon-cyan font-semibold mb-2 flex items-center">
                              <FiUser className="w-4 h-4 mr-2" />
                              Dados Pessoais
                            </h4>
                            <ul className="text-cyber-text text-sm space-y-1">
                              <li>• Nome e email</li>
                              <li>• Informações de perfil</li>
                              <li>• Histórico de navegação</li>
                              <li>• Preferências do usuário</li>
                            </ul>
                          </div>
                          <div className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                            <h4 className="text-neon-cyan font-semibold mb-2 flex items-center">
                              <FiLock className="w-4 h-4 mr-2" />
                              Dados Técnicos
                            </h4>
                            <ul className="text-cyber-text text-sm space-y-1">
                              <li>• Endereço IP</li>
                              <li>• Informações do dispositivo</li>
                              <li>• Cookies e sessões</li>
                              <li>• Analytics de uso</li>
                            </ul>
                          </div>
                        </div>
                      </section>

                      <section>
                        <h3 className="text-xl font-semibold text-white mb-4">
                          Bases Legais para Tratamento
                        </h3>
                        <div className="space-y-4">
                          {[
                            {
                              base: 'Consentimento',
                              description:
                                'Para newsletter, marketing e comunicações opcionais',
                              article: 'Art. 7°, I',
                            },
                            {
                              base: 'Legítimo Interesse',
                              description:
                                'Para analytics, melhorias do site e segurança',
                              article: 'Art. 7°, IX',
                            },
                            {
                              base: 'Execução de Contrato',
                              description:
                                'Para usuários cadastrados e serviços solicitados',
                              article: 'Art. 7°, V',
                            },
                            {
                              base: 'Cumprimento de Obrigação Legal',
                              description:
                                'Quando exigido por legislação aplicável',
                              article: 'Art. 7°, II',
                            },
                          ].map(({ base, description, article }, index) => (
                            <div
                              key={index}
                              className="bg-cyber-bg/30 p-4 rounded-lg border border-neon-cyan/20">
                              <div className="flex justify-between items-start mb-2">
                                <h4 className="text-white font-semibold">
                                  {base}
                                </h4>
                                <span className="text-neon-cyan text-sm font-mono">
                                  {article}
                                </span>
                              </div>
                              <p className="text-cyber-text text-sm">
                                {description}
                              </p>
                            </div>
                          ))}
                        </div>
                      </section>
                    </div>
                  </div>
                )}

                {activeTab === 'rights' && (
                  <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                    <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                      Seus Direitos
                    </h2>

                    <div className="space-y-6">
                      {[
                        {
                          title: 'Confirmação da Existência de Tratamento',
                          article: 'Art. 18, I',
                          description:
                            'Direito de confirmar se tratamos seus dados pessoais',
                          action:
                            'Solicitar confirmação sobre coleta e uso dos seus dados',
                        },
                        {
                          title: 'Acesso aos Dados',
                          article: 'Art. 18, II',
                          description:
                            'Direito de acessar todos os seus dados que possuímos',
                          action: 'Baixar relatório completo dos seus dados',
                        },
                        {
                          title: 'Correção de Dados',
                          article: 'Art. 18, III',
                          description:
                            'Direito de corrigir dados incompletos, inexatos ou desatualizados',
                          action:
                            'Solicitar correção de informações incorretas',
                        },
                        {
                          title: 'Anonimização ou Eliminação',
                          article: 'Art. 18, IV',
                          description:
                            'Direito de solicitar anonimização ou eliminação de dados desnecessários',
                          action: 'Requerer exclusão dos seus dados',
                        },
                        {
                          title: 'Portabilidade dos Dados',
                          article: 'Art. 18, V',
                          description:
                            'Direito de transferir seus dados para outro fornecedor',
                          action: 'Exportar dados em formato estruturado',
                        },
                        {
                          title:
                            'Eliminação de Dados Tratados com Consentimento',
                          article: 'Art. 18, VI',
                          description:
                            'Direito de eliminar dados processados com base no seu consentimento',
                          action: 'Revogar consentimento e solicitar exclusão',
                        },
                        {
                          title: 'Informação sobre Compartilhamento',
                          article: 'Art. 18, VII',
                          description:
                            'Direito de saber com quem compartilhamos seus dados',
                          action:
                            'Consultar lista de terceiros que acessam seus dados',
                        },
                        {
                          title: 'Revogação do Consentimento',
                          article: 'Art. 18, IX',
                          description:
                            'Direito de revogar consentimento a qualquer momento',
                          action:
                            'Cancelar newsletter e comunicações opcionais',
                        },
                      ].map((right, index) => (
                        <div
                          key={index}
                          className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="text-white font-semibold text-lg">
                              {right.title}
                            </h3>
                            <span className="text-neon-cyan text-sm font-mono bg-neon-cyan/10 px-2 py-1 rounded">
                              {right.article}
                            </span>
                          </div>
                          <p className="text-cyber-text mb-3">
                            {right.description}
                          </p>
                          <div className="bg-cyber-bg/50 p-3 rounded border border-neon-cyan/10">
                            <p className="text-neon-cyan text-sm font-medium">
                              Como exercer:
                            </p>
                            <p className="text-cyber-muted text-sm">
                              {right.action}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-8 p-6 bg-neon-cyan/10 rounded-lg border border-neon-cyan/30">
                      <h3 className="text-white font-semibold mb-3">
                        Importante:
                      </h3>
                      <ul className="text-cyber-text text-sm space-y-2">
                        <li>• Todos os direitos são gratuitos</li>
                        <li>• Prazo de resposta: até 15 dias úteis</li>
                        <li>
                          • Pode ser prorrogado por mais 15 dias em casos
                          complexos
                        </li>
                        <li>
                          • Negativas serão sempre justificadas por escrito
                        </li>
                      </ul>
                    </div>
                  </div>
                )}

                {activeTab === 'contact' && (
                  <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-cyber-surface">
                    <h2 className="text-3xl font-bold text-neon-cyan mb-6">
                      Canal de Comunicação
                    </h2>

                    <div className="space-y-6">
                      <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                        <h3 className="text-white font-semibold mb-4 flex items-center">
                          <FiMail className="w-5 h-5 mr-2 text-neon-cyan" />
                          Encarregado de Dados (DPO)
                        </h3>
                        <div className="space-y-3 text-cyber-text">
                          <p>
                            <strong>Email:</strong> <EMAIL>
                          </p>
                          <p>
                            <strong>Responsabilidade:</strong> Assegurar
                            conformidade com a LGPD
                          </p>
                          <p>
                            <strong>Atendimento:</strong> Segunda a sexta, 9h às
                            18h
                          </p>
                          <p>
                            <strong>Prazo de resposta:</strong> Até 15 dias
                            úteis
                          </p>
                        </div>
                      </div>

                      <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                        <h3 className="text-white font-semibold mb-4">
                          Formulário de Solicitação
                        </h3>
                        <form className="space-y-4">
                          <div>
                            <label className="block text-cyber-text mb-2">
                              Tipo de Solicitação
                            </label>
                            <select className="w-full bg-cyber-bg border border-cyber-surface rounded-lg px-4 py-3 text-white focus:border-neon-cyan focus:outline-none">
                              <option>Acesso aos dados</option>
                              <option>Correção de dados</option>
                              <option>Eliminação de dados</option>
                              <option>Portabilidade de dados</option>
                              <option>Revogação de consentimento</option>
                              <option>
                                Informações sobre compartilhamento
                              </option>
                              <option>Outro</option>
                            </select>
                          </div>
                          <div>
                            <label className="block text-cyber-text mb-2">
                              Email
                            </label>
                            <input
                              type="email"
                              className="w-full bg-cyber-bg border border-cyber-surface rounded-lg px-4 py-3 text-white focus:border-neon-cyan focus:outline-none"
                              placeholder="<EMAIL>"
                            />
                          </div>
                          <div>
                            <label className="block text-cyber-text mb-2">
                              Descrição da Solicitação
                            </label>
                            <textarea
                              rows={4}
                              className="w-full bg-cyber-bg border border-cyber-surface rounded-lg px-4 py-3 text-white focus:border-neon-cyan focus:outline-none resize-none"
                              placeholder="Descreva sua solicitação em detalhes..."
                            />
                          </div>
                          <button
                            type="submit"
                            className="w-full bg-neon-cyan text-cyber-bg font-semibold py-3 rounded-lg hover:bg-neon-cyan/80 transition-colors">
                            Enviar Solicitação
                          </button>
                        </form>
                      </div>

                      <div className="bg-cyber-bg/30 p-6 rounded-lg border border-neon-cyan/20">
                        <h3 className="text-white font-semibold mb-4">
                          Autoridade Nacional de Proteção de Dados (ANPD)
                        </h3>
                        <p className="text-cyber-text mb-4">
                          Caso não fique satisfeito com nossa resposta, você
                          pode entrar em contato com a autoridade de proteção de
                          dados competente:
                        </p>
                        <div className="space-y-2 text-cyber-text">
                          <p>
                            <strong>Site:</strong>{' '}
                            <a
                              href="https://www.gov.br/anpd/"
                              className="text-neon-cyan hover:underline"
                              target="_blank"
                              rel="noopener noreferrer">
                              gov.br/anpd
                            </a>
                          </p>
                          <p>
                            <strong>Email:</strong> <EMAIL>
                          </p>
                          <p>
                            <strong>Ouvidoria:</strong> Para denúncias e
                            reclamações
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                  <h3 className="text-white font-semibold mb-4 flex items-center">
                    <FiFileText className="w-5 h-5 mr-2 text-neon-cyan" />
                    Documentos Relacionados
                  </h3>
                  <div className="space-y-3">
                    <a
                      href="/privacy"
                      className="block text-cyber-text hover:text-neon-cyan transition-colors">
                      → Política de Privacidade
                    </a>
                    <a
                      href="/terms"
                      className="block text-cyber-text hover:text-neon-cyan transition-colors">
                      → Termos de Uso
                    </a>
                    <a
                      href="/cookies"
                      className="block text-cyber-text hover:text-neon-cyan transition-colors">
                      → Política de Cookies
                    </a>
                  </div>
                </div>

                <div className="bg-cyber-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-cyber-surface">
                  <h3 className="text-white font-semibold mb-4">
                    Status de Conformidade
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-cyber-text">LGPD</span>
                      <span className="text-green-400 text-sm">✓ Conforme</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cyber-text">
                        Mapeamento de Dados
                      </span>
                      <span className="text-green-400 text-sm">✓ Completo</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cyber-text">DPO Designado</span>
                      <span className="text-green-400 text-sm">✓ Ativo</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-cyber-text">Segurança</span>
                      <span className="text-green-400 text-sm">
                        ✓ Implementada
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-neon-cyan/10 backdrop-blur-sm rounded-2xl p-6 border border-neon-cyan/30">
                  <h3 className="text-white font-semibold mb-3">
                    Precisa de Ajuda?
                  </h3>
                  <p className="text-cyber-text text-sm mb-4">
                    Nossa equipe está pronta para esclarecer dúvidas sobre seus
                    dados.
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="block w-full text-center bg-neon-cyan text-cyber-bg font-semibold py-2 rounded-lg hover:bg-neon-cyan/80 transition-colors text-sm">
                    Entrar em Contato
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    </>
  );
};

export default LGPD;
